#!/bin/bash

# Run the Python installation script
python3 install_prerequisites.py

# Check if the script succeeded
if [ $? -eq 0 ]; then
    echo "[INFO] Installation completed successfully"
    echo "[INFO] Activating environment..."
    
    # Source the activation script created by Python
    if [ -f /tmp/activate_env.sh ]; then
        source /tmp/activate_env.sh
    else
        echo "[ERROR] Activation script not found"
        exit 1
    fi
else
    echo "[ERROR] Installation failed"
    exit 1
fi