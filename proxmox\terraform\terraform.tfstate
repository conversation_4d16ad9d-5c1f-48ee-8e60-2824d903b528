{"version": 4, "terraform_version": "1.12.2", "serial": 3, "lineage": "8f56dfca-6600-7f40-fc37-cbb7bbd51db0", "outputs": {"cluster_kubeconfig": {"value": "/home/<USER>/dev/dev-eks-a-cluster.kubeconfig", "type": "string"}, "headlamp_host": {"value": "headlamp.dev.home.lan", "type": "string"}, "ingress_hint": {"value": "If NodePort: http://<node-ip>:<http-nodeport>. If LoadBalancer: map headlamp.dev.home.lan -> *************", "type": "string"}, "runbook_notes": {"value": "Cluster      : dev\r\nDomain       : home.lan\r\nMetalLB Pool : *************-*************\r\nIngress IP   : *************\r\nNFS Base     : /srv/eks\r\nNFS Clients  : ***********/24\r\nHost (NFS)   : *************\r\n", "type": "string"}}, "resources": [], "check_results": null}