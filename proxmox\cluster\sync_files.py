#!/usr/bin/env python3
"""
Secure file sync script using scp module with permissive 777 permissions.
"""

import os
import sys
import keyring
import getpass
import argparse
from scp import SCPClient, SCPException
import paramiko

# ANSI colors
GREEN = '\033[92m'
BLUE = '\033[94m'
RED = '\033[91m'
YELLOW = '\033[93m'
NC = '\033[0m'  # No Color

# Remote server details
REMOTE_USER = "admin"
REMOTE_HOST = "node-01.local"
DEFAULT_REMOTE_PATH = "/home/<USER>"  # Persistent directory, survives reboots unlike /tmp

# Files to sync
FILES_TO_SYNC = ["os_prep.py", "eksa-all-in-one.yaml", "cluster_setup.py", "install_prereqs.sh", "requirements.txt"]

# Service name for keyring
SERVICE_ID = "eks_anywhere_sync"

def log_info(msg):
    print(f"{BLUE}[info]{NC} {msg}")

def log_success(msg):
    print(f"{GREEN}[✓]{NC} {msg}")

def log_error(msg):
    print(f"{RED}[error]{NC} {msg}")

def log_warning(msg):
    print(f"{YELLOW}[warn]{NC} {msg}")

def get_password():
    """Get password from keyring or prompt user and auto-save it."""
    password = keyring.get_password(SERVICE_ID, REMOTE_USER)
    if not password:
        log_info(f"No stored password found for {REMOTE_USER}")
        password = getpass.getpass(f"Enter password for {REMOTE_USER}: ")
        keyring.set_password(SERVICE_ID, REMOTE_USER, password)
        log_success("Password automatically stored in keyring")
    return password

def ensure_remote_path(ssh, password, remote_path):
    """Ensure remote path exists, set permissions, and clean up existing files."""
    try:
        # First check if we can write to the path without sudo
        stdin, stdout, stderr = ssh.exec_command(f"mkdir -p {remote_path} 2>/dev/null && touch {remote_path}/.write_test && rm {remote_path}/.write_test")
        if stdout.channel.recv_exit_status() == 0:
            log_success(f"User has write access to {remote_path}")
            return True
        
        # Try with sudo if direct access failed
        log_info(f"Attempting to set permissions with sudo for {remote_path}...")
        stdin, stdout, stderr = ssh.exec_command(f"sudo -S mkdir -p {remote_path} && sudo -S chmod 777 {remote_path}")
        stdin.write(password + '\n')
        stdin.flush()
        if stdout.channel.recv_exit_status() != 0:
            error = stderr.read().decode().strip()
            log_error(f"Failed to set permissions: {error}")
            if remote_path != DEFAULT_REMOTE_PATH:
                log_warning(f"Will use fallback path: {DEFAULT_REMOTE_PATH}")
            return False
        
        # Handle existing files
        for file in FILES_TO_SYNC:
            cmd = f'sudo -S chmod 777 {remote_path}/{file} 2>/dev/null || sudo -S rm {remote_path}/{file} 2>/dev/null'
            stdin, stdout, stderr = ssh.exec_command(cmd)
            stdin.write(password + '\n')
            stdin.flush()
            log_info(f"Prepared target file: {file}")
        
        log_success(f"Set permissions and cleaned up existing files in {remote_path}")
        return True
    except Exception as e:
        log_error(f"Error setting up path: {e}")
        return False

def sync_files(remote_path=DEFAULT_REMOTE_PATH):
    """Sync files to remote server using SCPClient."""
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        ssh = None
        scp = None
        try:
            password = get_password()
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            log_info(f"Connecting to {REMOTE_USER}@{REMOTE_HOST}...")
            ssh.connect(REMOTE_HOST, username=REMOTE_USER, password=password)
            
            # Set permissive permissions
            use_fallback = not ensure_remote_path(ssh, password, remote_path)
            effective_path = DEFAULT_REMOTE_PATH if use_fallback else remote_path
            
            scp = SCPClient(ssh.get_transport())
            copied_files = []
            
            for file in FILES_TO_SYNC:
                if not os.path.exists(file):
                    log_error(f"File not found: {file}")
                    continue
                log_info(f"Copying {file}...")
                target_path = f"{effective_path}/{file}"
                try:
                    scp.put(file, remote_path=target_path)
                    log_success(f"Successfully copied {file} to {effective_path}")
                    copied_files.append(file)
                    if file.endswith('.sh') or file.endswith('.py'):
                        ssh.exec_command(f"chmod +x {target_path}")
                        log_info(f"Made {file} executable")
                except SCPException as e:
                    log_error(f"Failed to copy {file}: {e}")
            
            if copied_files:
                log_success("File sync completed")
                log_info(f"Files copied to {effective_path}: {', '.join(copied_files)}")
            else:
                log_error("No files were successfully copied")
            return
        
        except Exception as e:
            log_error(f"Sync failed: {str(e)}")
            if "authentication failed" in str(e).lower():
                keyring.delete_password(SERVICE_ID, REMOTE_USER)
                log_info("Removed stored password due to authentication failure")
                retry_count += 1
                if retry_count < max_retries:
                    log_info(f"Retry {retry_count}/{max_retries-1} - Please enter the correct password:")
                    continue
                log_error("Maximum retry attempts reached. Please check your credentials.")
                sys.exit(1)
            sys.exit(1)
        finally:
            if scp:
                scp.close()
            if ssh:
                ssh.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Secure file sync to remote server")
    parser.add_argument("--path", "-p", default=DEFAULT_REMOTE_PATH, 
                        help=f"Remote path to sync files to (default: {DEFAULT_REMOTE_PATH})")
    args = parser.parse_args()
    
    sync_files(args.path)