#!/usr/bin/env python3
"""
XCP-ng Storage Setup (refactored, no extra CLI flags)

• Preserves interactive selection & wipe prompt
• Fewer lines via helper utilities and Rich tables
• Safe `subprocess.run` wrapper (no silent failures)
• HTTPS-ready XenAPI login (falls back to HTTP)
• Added Rich menu for disk selection on wipe skip
"""

import getpass
import logging
import shlex
import subprocess
import sys
import uuid
from pathlib import Path
from typing import Iterable, Sequence, Union

import pandas as pd
import XenAPI
import keyring
from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt
import tty
import termios

# ─────────────────────────── Configuration ───────────────────────────

DATA_DISKS      = ["/dev/sdb", "/dev/sdc"]     # DO NOT include /dev/sda
LOCAL_SR_NAME   = "bulk-local-sr"
NFS_SR_NAME     = "nfs-iso"
NFS_SERVER      = "*************"
NFS_PATH        = "/volume1/base-iso"
XAPI_ENDPOINT   = "http://localhost"           # swap to https://<host> if TLS enabled

# ───────────────────────────── Utilities ──────────────────────────────

console = Console()
LOG     = logging.getLogger("xcp")
logging.basicConfig(
    level=logging.INFO,
    format="[%(levelname)s] %(message)s",
)

def run(cmd: Union[Sequence[str], str], *, quiet: bool = False) -> str:
    """Run shell command, return stdout, raise on error."""
    if isinstance(cmd, str):
        cmd_shell, shell = cmd, True
    else:
        cmd_shell, shell = shlex.join(cmd), False

    if not quiet:
        LOG.debug("➜ %s", cmd_shell)

    proc = subprocess.run(cmd, shell=shell, text=True,
                          capture_output=True, check=False)
    if proc.returncode:
        raise RuntimeError(
            f"{cmd_shell} → {proc.returncode}\n{proc.stderr or proc.stdout}"
        )
    return proc.stdout.strip()

# ───────────────────── Disk inspection helpers ───────────────────────

def smart_summary(disk: str) -> dict:
    model = family = serial = cap = "-"
    for line in run(["smartctl", "-i", disk], quiet=True).splitlines():
        if line.startswith("Model Family:"):
            family = line.split(":", 1)[1].strip()
        elif line.startswith("Device Model:"):
            model = line.split(":", 1)[1].strip()
        elif line.startswith("Serial Number:"):
            serial = line.split(":", 1)[1].strip()
        elif line.startswith("User Capacity:"):
            cap = line.split("[")[-1].split("]")[0]
    return {"Model": f"{family} {model}".strip(), "Serial": serial, "Capacity": cap}

def partition_type(disk: str) -> str:
    return run(["blkid", "-s", "PTTYPE", "-o", "value", disk], quiet=True) or "-"

def fs_type(part: str) -> str:
    return run(["lsblk", "-n", "-o", "FSTYPE", part], quiet=True) or "-"

def display_disks(disks: Iterable[str]) -> None:
    rows = []
    for d in disks:
        info = smart_summary(d)
        rows.append(
            {
                "Disk": d,
                **info,
                "PT": partition_type(d),
                "FS": fs_type(f"{d}1"),
            }
        )
    df = pd.DataFrame(rows)
    table = Table(show_edge=False)
    for col in df.columns:
        table.add_column(col, overflow="fold")
    for _, row in df.iterrows():
        table.add_row(*map(str, row.tolist()))
    console.print("\n📋 [bold]Disk information[/bold]")
    console.print(table)

def select_disks(disks: Iterable[str]) -> list:
    """Prompt user to select disks to import using Rich."""
    disk_list = list(disks)
    
    console.print("[bold]Available disks:[/bold]")
    for i, disk in enumerate(disk_list):
        console.print(f"  {i+1}: {disk}")
    console.print(f"  all: All disks")
    
    valid_choices = [str(i+1) for i in range(len(disk_list))] + ["all"]
    choice = Prompt.ask(
        "\nSelect disk number to import",
        choices=valid_choices,
        default="all",
    )
    
    if choice == "all":
        return disk_list
    return [disk_list[int(choice) - 1]]

def wipe_and_partition(disk: str) -> None:
    cmds = [
        ["wipefs", "-af", disk],
        ["sgdisk", "--zap-all", disk],
        ["parted", "-s", disk, "mklabel", "gpt"],
        ["parted", "-s", disk, "mkpart", "primary", "0%", "100%"],
        ["wipefs", "-af", f"{disk}1"],
    ]
    for c in cmds:
        run(c, quiet=True)

# ───────────────────── XenAPI convenience wrappers ───────────────────

def get_xcp_password() -> str:
    """Get XCP-ng root password from keyring or prompt user."""
    service_name = "xcp-ng-storage-setup"
    username = "root"
    
    password = keyring.get_password(service_name, username)
    
    if password:
        console.print(f"[green]Using saved password for {username}[/green]")
        return password
    
    password = getpass.getpass(f"Root password for XenAPI ({username}): ")
    keyring.set_password(service_name, username, password)
    console.print("[green]Password saved to keyring[/green]")
    
    return password

def xen_session(url: str, password: str) -> XenAPI.Session:
    ses = XenAPI.Session(url)
    ses.login_with_password("root", password)
    return ses

def display_combined_overview(sess: XenAPI.Session, disks: Iterable[str]) -> None:
    """Display unified storage and disk information."""
    storage_info = {}
    used_devices = set()
    used_base_disks = set()

    try:
        srs = sess.xenapi.SR.get_all()
        for sr in srs:
            sr_record = sess.xenapi.SR.get_record(sr)
            if sr_record['type'] in ['lvm', 'nfs']:
                details = "-"
                sr_type = sr_record['type'].upper()
                size = f"{int(sr_record['physical_size']) // (1024**3)} GB" if sr_record['physical_size'] != '0' else "-"
                used = f"{int(sr_record['physical_utilisation']) // (1024**3)} GB" if sr_record['physical_utilisation'] != '0' else "-"
                pbds = sr_record['PBDs']
                if pbds:
                    pbd_record = sess.xenapi.PBD.get_record(pbds[0])
                    device_config = pbd_record['device_config']
                    if sr_record['type'] == 'lvm':
                        raw_devices = device_config.get('device', '-')
                        if raw_devices != '-':
                            devices_list = []
                            for dev in raw_devices.split(','):
                                dev = dev.strip()
                                if '/disk/by-id/' in dev:
                                    parts = dev.rsplit('-part', 1)
                                    if len(parts) == 2:
                                        base_disk = parts[0]
                                    else:
                                        base_disk = dev
                                else:
                                    disk_name = Path(dev).name.rstrip('0123456789')
                                    base_disk = f'/dev/{disk_name}'
                                used_base_disks.add(base_disk)
                                info = smart_summary(base_disk)
                                devices_list.append(info['Model'])
                            details = "\n".join(devices_list)
                            used_devices.update(raw_devices.split(','))
                    elif sr_record['type'] == 'nfs':
                        server = device_config.get('server', '')
                        path = device_config.get('serverpath', '')
                        details = f"{server}:{path}" if server and path else "-"
                storage_info[sr_record['name_label']] = {
                    "SR Type": sr_type,
                    "Size": size,
                    "Used": used,
                    "Details": details
                }
    except Exception as e:
        LOG.warning("Could not retrieve storage information: %s", e)
    
    rows = []
    for name, info in storage_info.items():
        rows.append({
            "Type": "Storage",
            "Name": name,
            "SR Type": info["SR Type"],
            "Size": info["Size"],
            "Used": info["Used"],
            "Details": info["Details"]
        })
    
    for d in disks:
        if d not in used_base_disks:
            disk_info = smart_summary(d)
            rows.append({
                "Type": "Unused Disk",
                "Name": d,
                "SR Type": "-",
                "Size": disk_info["Capacity"],
                "Used": "-",
                "Details": disk_info['Model']            })
    
    if rows:
        df = pd.DataFrame(rows)
        # Always sort by Name column
        df = df.sort_values(by="Name")
        table = Table(show_edge=False)
        for col in df.columns:
            table.add_column(col, overflow="fold")
        for _, row in df.iterrows():
            table.add_row(*map(str, row.tolist()))
        console.print("\n💾📋 [bold]Storage & Disk Overview[/bold]")
        console.print(table)
    
    return used_devices

def sr_exists(ses: XenAPI.Session, label: str) -> bool:
    """Check if storage repository exists."""
    return bool(ses.xenapi.SR.get_by_name_label(label))

def import_existing_pv(disk: str) -> None:
    """Import existing PV without wiping and ensure VG is activated."""
    try:
        result = run(["pvs", f"{disk}1"], quiet=True)
        if result:
            LOG.info("PV %s1 already exists, scanning for VG", disk)
            run(["vgscan"])
            run(["vgchange", "-ay"])
        else:
            LOG.info("Creating PV on %s1", disk)
            run(["pvcreate", "-ff", "--yes", f"{disk}1"])
    except RuntimeError:
        LOG.info("Creating new PV on %s1", disk)
        run(["pvcreate", "-ff", "--yes", f"{disk}1"])

def introduce_lvm_sr(ses: XenAPI.Session, parts: Sequence[str], name: str) -> None:
    host = ses.xenapi.host.get_all()[0]
    cfg  = {"device": ",".join(parts)}
    sr   = ses.xenapi.SR.create(host, cfg, "0", name, "", "lvm", "user", False, {})
    ses.xenapi.SR.scan(sr)

def introduce_nfs_sr(ses: XenAPI.Session, server: str, path: str, name: str) -> None:
    host = ses.xenapi.host.get_all()[0]
    cfg  = {"server": server, "serverpath": path}
    sr   = ses.xenapi.SR.create(host, cfg, "0", name, "", "nfs", "user", True, {})
    ses.xenapi.SR.scan(sr)

def delete_storage_repository(password: str) -> None:
    """Delete an existing storage repository."""
    sess = xen_session(XAPI_ENDPOINT, password)
    try:
        # Get all SRs except system ones
        srs = sess.xenapi.SR.get_all()
        deletable_srs = []
        
        for sr in srs:
            sr_record = sess.xenapi.SR.get_record(sr)
            # Skip system SRs and ISO/DVD SRs
            if (sr_record['type'] in ['lvm', 'nfs'] and 
                sr_record['name_label'] not in ['Local storage', 'DVD drives']):
                deletable_srs.append({
                    'ref': sr,
                    'name': sr_record['name_label'],
                    'type': sr_record['type'].upper(),
                    'size': f"{int(sr_record['physical_size']) // (1024**3)} GB" if sr_record['physical_size'] != '0' else "-"
                })
        
        if not deletable_srs:
            console.print("\n[yellow]No deletable storage repositories found.[/yellow]")
            return
        
        console.print("\n[bold]Deletable Storage Repositories:[/bold]")
        for i, sr_info in enumerate(deletable_srs):
            console.print(f"  {i+1}: {sr_info['name']} ({sr_info['type']}, {sr_info['size']})")
        console.print(f"  {len(deletable_srs)+1}: Cancel")
        valid_choices = [str(i+1) for i in range(len(deletable_srs) + 1)]
        print("\nPress a number key to select: ", end="", flush=True)
        
        choice = get_single_key()
        console.print(choice)
        if choice in valid_choices:
            choice_idx = int(choice) - 1
        else:
            choice_idx = len(deletable_srs)  # Default to Cancel
        
        if choice_idx >= len(deletable_srs):
            console.print("[yellow]Operation cancelled.[/yellow]")
            return
        sr_to_delete = deletable_srs[choice_idx]        # For NFS, offer disconnect, forget options
        if sr_to_delete['type'] == 'NFS':
            console.print(f"\n📁 Options for NFS SR '{sr_to_delete['name']}':")
            console.print("  d: Disconnect (keeps data, just unplugs from XCP-ng)")
            console.print("  f: Forget (removes SR from XCP-ng, keeps NFS data)")
            console.print("  n: Cancel")
            print("\nPress a key (d/f/n): ", end="", flush=True)
            
            action_char = get_single_key().lower()
            console.print(action_char)
            if action_char not in ['d', 'f', 'n']:
                action_char = 'n'  # Default to cancel
            
            if action_char == 'n':
                console.print("[yellow]Operation cancelled.[/yellow]")
                return
            elif action_char == 'd':
                # Just disconnect (unplug PBDs)
                try:
                    pbds = sess.xenapi.SR.get_PBDs(sr_to_delete['ref'])
                    for pbd in pbds:
                        sess.xenapi.PBD.unplug(pbd)
                    LOG.info("Successfully disconnected NFS SR '%s'", sr_to_delete['name'])
                    console.print(f"[green]✅ Successfully disconnected NFS SR '{sr_to_delete['name']}' (data preserved)[/green]")
                except Exception as e:
                    LOG.error("Failed to disconnect NFS SR '%s': %s", sr_to_delete['name'], e)
                    console.print(f"[red]❌ Failed to disconnect SR: {e}[/red]")
                return
            elif action_char == 'f':
                # Forget SR (removes from XCP-ng but keeps NFS data)
                console.print(f"\n⚠️  Are you sure you want to FORGET SR '{sr_to_delete['name']}'?")
                console.print("You will lose all the metadata, meaning all the links between")
                console.print("the VDIs (disks) and their respective VMs. This operation cannot be undone.")
                console.print("(NFS data remains safe on the server)")
                console.print("\nType 'FORGET' to confirm: ", end="")
                confirm_text = input().strip()
                if confirm_text == 'FORGET':
                    try:
                        # First unplug all PBDs (required before forgetting)
                        pbds = sess.xenapi.SR.get_PBDs(sr_to_delete['ref'])
                        for pbd in pbds:
                            sess.xenapi.PBD.unplug(pbd)
                        
                        # Now forget the SR (this removes it from XCP-ng database)
                        sess.xenapi.SR.forget(sr_to_delete['ref'])
                        LOG.info("Successfully forgot NFS SR '%s'", sr_to_delete['name'])
                        console.print(f"[green]✅ Successfully forgot NFS SR '{sr_to_delete['name']}' (NFS data preserved)[/green]")
                    except Exception as e:
                        LOG.error("Failed to forget NFS SR '%s': %s", sr_to_delete['name'], e)
                        console.print(f"[red]❌ Failed to forget SR: {e}[/red]")
                else:
                    console.print("[yellow]Forget operation cancelled - confirmation text did not match.[/yellow]")
                return
        else:
            # For LVM, only offer delete option
            console.print(f"\n⚠️  Are you sure you want to delete SR '{sr_to_delete['name']}'? This will destroy all data!")
        
        console.print("  y: Yes, delete it")
        console.print("  n: No, cancel")
        print("\nPress a key (y/n): ", end="", flush=True)
        
        confirm_char = get_single_key().lower()
        console.print(confirm_char)
        if confirm_char not in ['y', 'n']:
            confirm_char = 'n'  # Default to no
        
        if confirm_char == 'y':
            try:
                # Get PBDs and unplug them first
                pbds = sess.xenapi.SR.get_PBDs(sr_to_delete['ref'])
                for pbd in pbds:
                    sess.xenapi.PBD.unplug(pbd)
                
                # Destroy the SR
                sess.xenapi.SR.destroy(sr_to_delete['ref'])
                LOG.info("Successfully deleted SR '%s'", sr_to_delete['name'])
                console.print(f"[green]✅ Successfully deleted SR '{sr_to_delete['name']}'[/green]")
            except Exception as e:
                LOG.error("Failed to delete SR '%s': %s", sr_to_delete['name'], e)
                console.print(f"[red]❌ Failed to delete SR: {e}[/red]")
        else:
            console.print("[yellow]Deletion cancelled.[/yellow]")
            
    finally:
        sess.logout()

# ────────────────────────────── Main flow ────────────────────────────

def get_single_key() -> str:
    """Read a single key press in Linux."""
    fd = sys.stdin.fileno()
    old_settings = termios.tcgetattr(fd)
    try:
        tty.setraw(fd)
        char = sys.stdin.read(1)
    finally:
        termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
    return char

def show_main_menu(pw: str) -> bool:
    """Show main menu and handle user choices. Returns True to continue, False to exit."""
    console.print("\n" + "="*80)
    console.rule("[bold blue]XCP-ng Storage Setup - Main Menu")
    
    sess = xen_session(XAPI_ENDPOINT, pw)
    try:
        used_devices = display_combined_overview(sess, DATA_DISKS)
    except Exception as e:
        LOG.warning("Could not retrieve storage information: %s", e)
        used_devices = set()
    finally:
        sess.logout()

    available_disks = []
    for disk in DATA_DISKS:
        disk_partition = f"{disk}1"
        is_used_disk = any(
            disk in device or disk_partition in device or device.endswith(disk.split('/')[-1]) 
            for device in used_devices
        )
        if not is_used_disk:
            available_disks.append(disk)    # Build menu options
    console.print("\n[bold cyan]Available Actions:[/bold cyan]")
    options = []
    option_num = 1
    
    if available_disks:
        console.print(f"  {option_num}: 💽 Setup storage with available disks")
        options.append("setup_storage")
        option_num += 1
    
    console.print(f"  {option_num}: 🗑️  Delete existing storage repository")
    options.append("delete_storage")
    option_num += 1
    console.print(f"  {option_num}: 🔄 Refresh storage overview")
    options.append("refresh")
    option_num += 1
    
    console.print(f"  {option_num}: ❌ Exit")
    options.append("exit")
    
    valid_choices = [str(i) for i in range(1, len(options) + 1)]
    print("\nPress a number key to select: ", end="", flush=True)
    
    choice = get_single_key()
    console.print(choice)
    if choice in valid_choices:
        selected_option = options[int(choice) - 1]
    else:
        choice = str(len(options))  # Default to exit
        selected_option = options[int(choice) - 1]
    
    if selected_option == "exit":
        console.print("[yellow]Goodbye![/yellow]")
        return False
    elif selected_option == "refresh":
        return True  # Just refresh by showing menu again
    elif selected_option == "delete_storage":
        delete_storage_repository(pw)
        return True
    elif selected_option == "setup_storage":
        setup_storage_workflow(pw, available_disks)
        return True
    
    return True

def setup_storage_workflow(pw: str, available_disks: list) -> None:
    """Handle the storage setup workflow."""
    
    wipe_response = console.input("\n⚠️  Wipe / CREATE PVs on these disks? (y/N): ").lower().strip()
    
    if wipe_response == "y":
        selected_disks = available_disks
        for d in available_disks:
            wipe_and_partition(d)
            run(["pvcreate", "-ff", "--yes", f"{d}1"])
    else:
        console.print("\n[yellow]Skipping wipe. Select disks to import.[/yellow]")
        selected_disks = select_disks(available_disks)
        if not selected_disks:
            console.print("[yellow]No disks selected. Aborted.[/yellow]")
            sys.exit(0)
        
        # Import existing PVs for selected disks
        for d in selected_disks:
            import_existing_pv(d)    # Enhanced Storage Repository Options
    console.print("\n[bold]📁 Storage Repository Options:[/bold]")
    
    # Create new storage repository
    target_sr_name = Prompt.ask("\n📝 Enter name for new storage repository", default=LOCAL_SR_NAME)

    # 5. XenAPI login and storage operations
    sess = xen_session(XAPI_ENDPOINT, pw)

    try:
        parts = [f"{d}1" for d in selected_disks]

        # Create new SR
        LOG.info("➕ Creating new SR '%s'", target_sr_name)
        introduce_lvm_sr(sess, parts, target_sr_name)

        # Handle NFS SR separately
        if sr_exists(sess, NFS_SR_NAME):
            LOG.info("NFS SR '%s' already exists – skipping", NFS_SR_NAME)
        else:
            introduce_nfs_sr(sess, NFS_SERVER, NFS_PATH, NFS_SR_NAME)

    finally:
        sess.logout()

    # 6. Show final storage overview
    console.print("\n📊 [bold]Final Storage Overview[/bold]")
    sess = xen_session(XAPI_ENDPOINT, pw)
    try:
        display_combined_overview(sess, DATA_DISKS)
    except Exception as e:
        LOG.warning("Could not retrieve final storage information: %s", e)
    finally:
        sess.logout()

    console.print("\n✅  All done! Check XenOrchestra or XenCenter → Storage for your repositories.")

def main() -> None:
    """Main function that manages the application loop."""
    console.rule("[bold blue]XCP-ng Storage Setup")
    pw = get_xcp_password()
    
    # Main application loop
    while True:
        if not show_main_menu(pw):
            break  # Exit if show_main_menu returns False

if __name__ == "__main__":
    try:
        main()
    except Exception as exc:   # pylint: disable=broad-exception-caught
        LOG.error("%s", exc)
        sys.exit(1)