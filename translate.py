import os
import getpass
import keyring
import subprocess
import tempfile
import time
import hashlib
import json
from datetime import datetime
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from rich.panel import Panel
from openai import OpenAI
import re
try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

# Configuration variables
WHISPER_MODEL = "whisper-1"
PAPER_FORMATTING_MODEL = "gpt-4-turbo-2024-04-09"
DEFAULT_O3_MODEL = "o3-2025-04-16"
SUPPORTED_AUDIO_FORMATS = ['.opus', '.mp3', '.wav', '.m4a', '.mp4', '.mpeg', '.mpga', '.oga', '.ogg', '.webm', '.flac']
OUTPUT_DIR = "output"
KEYRING_SERVICE_NAME = "openai_whisper"
MAX_RESEARCH_TOKENS = 1500
MAX_PAPER_TOKENS = 2000
RESEARCH_TEMPERATURE = 0.7
PAPER_TEMPERATURE = 0.3

def get_file_hash(file_path):
    """Generate SHA-256 hash of a file for caching purposes"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def get_cache_file_path():
    """Get the path to the transcript cache file"""
    cache_dir = os.path.join(OUTPUT_DIR, ".cache")
    os.makedirs(cache_dir, exist_ok=True)
    return os.path.join(cache_dir, "transcript_cache.json")

def load_transcript_cache():
    """Load the transcript cache from disk"""
    cache_file = get_cache_file_path()
    if os.path.exists(cache_file):
        try:
            with open(cache_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return {}
    return {}

def save_transcript_cache(cache):
    """Save the transcript cache to disk"""
    cache_file = get_cache_file_path()
    try:
        with open(cache_file, "w", encoding="utf-8") as f:
            json.dump(cache, f, indent=2, ensure_ascii=False)
    except IOError:
        pass  # Silently fail if we can't save cache

def get_cached_transcript(file_path, console):
    """Check if we have a cached transcript for this file"""
    file_hash = get_file_hash(file_path)
    cache = load_transcript_cache()
    
    if file_hash in cache:
        cache_entry = cache[file_hash]
        console.print(f"[yellow]Found cached transcript for this file (hash: {file_hash[:8]}...)[/yellow]")
        console.print(f"[cyan]Original file: {cache_entry['original_filename']}[/cyan]")
        console.print(f"[cyan]Transcribed on: {cache_entry['timestamp']}[/cyan]")
        return cache_entry['transcript'], cache_entry['output_path']
    
    return None, None

def cache_transcript(file_path, transcript_text, output_path, original_filename):
    """Cache a transcript for future use"""
    file_hash = get_file_hash(file_path)
    cache = load_transcript_cache()
    
    cache[file_hash] = {
        'transcript': transcript_text,
        'output_path': output_path,
        'original_filename': original_filename,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'file_size': os.path.getsize(file_path)
    }
    
    save_transcript_cache(cache)

def get_audio_files():
    supported_formats = ['.opus', '.mp3', '.wav', '.m4a', '.mp4', '.mpeg', '.mpga', '.oga', '.ogg', '.webm', '.flac']
    return [file for file in os.listdir('.') if any(file.lower().endswith(fmt) for fmt in supported_formats)]

def select_audio_file(console):
    audio_files = get_audio_files()
    if not audio_files:
        console.print("[red]No audio files found in the current directory.[/red]")
        return None
    console.print("\n[bold cyan]Available audio files:[/bold cyan]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Index", style="dim", width=6)
    table.add_column("Filename", style="green")
    table.add_column("Size", style="yellow")
    for i, file in enumerate(audio_files, 1):
        size_mb = os.path.getsize(file) / (1024 * 1024)
        table.add_row(str(i), file, f"{size_mb:.2f} MB")
    console.print(table)
    while True:
        try:
            choice = Prompt.ask("\nSelect audio file", choices=[str(i) for i in range(1, len(audio_files) + 1)])
            return audio_files[int(choice) - 1]
        except (ValueError, IndexError):
            console.print("[red]Invalid selection. Please try again.[/red]")

def create_output_folder():
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def save_transcript(transcript_text, original_filename, output_dir):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_name = os.path.splitext(original_filename)[0]
    output_filename = f"{base_name}_{timestamp}_transcript.txt"
    output_path = os.path.join(output_dir, output_filename)
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(f"Transcript of: {original_filename}\nGenerated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n{'-' * 50}\n\n{transcript_text}")
    return output_path

def get_or_prompt_api_key():
    service_name = "openai_whisper"
    console = Console()
    saved_keys = {}
    for key_name in ["default", "key1", "key2", "key3", "personal", "work"]:
        key = keyring.get_password(service_name, key_name)
        if key:
            masked_key = f"{key[:3]}...{key[-2:]}" if len(key) >= 5 else key[:3]
            saved_keys[key_name] = {"key": key, "masked": masked_key}
    
    if saved_keys:
        if len(saved_keys) == 1:
            key_name, key_info = next(iter(saved_keys.items()))
            api_key = key_info["key"]
            console.print(f"[green]Automatically using saved API key '{key_name}'.[/green]")
        else:
            console.print("\n[bold cyan]Saved API Keys:[/bold cyan]")
            keys_table = Table(show_header=True, header_style="bold magenta")
            keys_table.add_column("Option", style="dim", width=8)
            keys_table.add_column("Key Name", style="green")
            keys_table.add_column("Key Preview", style="yellow")
            options = ["new"]
            keys_table.add_row("1", "Add New Key", "Enter a new API key")
            for i, (key_name, key_info) in enumerate(saved_keys.items(), 2):
                options.append(key_name)
                keys_table.add_row(str(i), key_name.title(), key_info["masked"])
            keys_table.add_row(str(len(options) + 1), "Exit", "Exit the application")
            console.print(keys_table)
            choice = Prompt.ask("\nSelect API key", choices=[str(i) for i in range(1, len(options) + 2)], default="2")
            choice_idx = int(choice) - 1
            if choice_idx == len(options):
                console.print("[yellow]Exiting...[/yellow]")
                exit()
            elif choice_idx == 0:
                next_key_num = len(saved_keys) + 1
                key_name = f"key{next_key_num}"
                api_key = getpass.getpass("Enter your OpenAI API key: ")
                keyring.set_password(service_name, key_name, api_key)
                console.print(f"[green]API key '{key_name}' saved to keyring.[/green]")
            else:
                selected_key_name = options[choice_idx]
                api_key = saved_keys[selected_key_name]["key"]
                console.print(f"[green]Using saved API key '{selected_key_name}'.[/green]")
    else:
        key_name = "key1"
        api_key = getpass.getpass("Enter your OpenAI API key: ")
        keyring.set_password(service_name, key_name, api_key)
        console.print(f"[green]API key '{key_name}' saved to keyring.[/green]")
    if len(api_key) >= 5:
        console.print(f"[cyan]Using API Key: {api_key[:3]}...{api_key[-2:]}[/cyan]")
    return api_key

def list_available_models(client, console):
    try:
        with console.status("[bold cyan]Fetching available models..."):
            models = client.models.list()
        console.print("\n[bold cyan]Available O-Series Models:[/bold cyan]")
        models_table = Table(show_header=True, header_style="bold magenta")
        models_table.add_column("Index", style="dim", width=6)
        models_table.add_column("Model ID", style="green")
        models_table.add_column("Created", style="dim")
        o_models = [model for model in models.data if re.match(r'^o\d', model.id) and not model.id.startswith('whisper') and not model.id.startswith('o1') and not model.id.startswith('o4') and not model.id.startswith('o3-pro')]
        
        unique_models = {}
        for model in o_models:
            base_without_date = re.sub(r'-\d{4}-\d{2}-\d{2}$', '', model.id)
            base_key = base_without_date
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', model.id)
            model_date = date_match.group(1) if date_match else "0000-00-00"
            if base_key not in unique_models or model_date > (re.search(r'(\d{4}-\d{2}-\d{2})', unique_models[base_key].id) or ["0000-00-00"])[0]:
                unique_models[base_key] = model
        
        sorted_models = sorted(unique_models.values(), key=lambda x: x.id)
        for i, model in enumerate(sorted_models, 1):
            created_date = datetime.fromtimestamp(model.created).strftime('%Y-%m-%d')
            models_table.add_row(str(i), model.id, created_date)
        
        console.print(models_table)
        console.print(f"\n[cyan]Total O models available: {len(sorted_models)}[/cyan]")
        console.print("\n[bold yellow]Model Selection:[/bold yellow]")
        console.print("Enter model number to [green]use for research[/green], 'details <number>' for info, 'back' for main menu, or 'exit' to quit")
        
        while True:
            choice = Prompt.ask("Your choice", default="back")
            if choice == "exit":
                console.print("[yellow]Exiting...[/yellow]")
                exit()
            elif choice == "back":
                return [model.id for model in sorted_models]
            elif choice.startswith("details"):
                try:
                    model_num = int(choice.split()[1])
                    selected_model = sorted_models[model_num - 1]
                    console.print(f"\n[cyan]Fetching detailed information for {selected_model.id}...[/cyan]")
                    detailed_model = client.models.retrieve(selected_model.id)
                    console.print(f"\n[bold green]Detailed information for {selected_model.id}:[/bold green]")
                    detail_table = Table(show_header=True, header_style="bold magenta")
                    detail_table.add_column("Property", style="cyan")
                    detail_table.add_column("Value", style="green")
                    detail_table.add_row("ID", detailed_model.id)
                    detail_table.add_row("Object", getattr(detailed_model, 'object', 'unknown'))
                    detail_table.add_row("Created", datetime.fromtimestamp(detailed_model.created).strftime('%Y-%m-%d %H:%M:%S'))
                    console.print(detail_table)
                except Exception as e:
                    console.print(f"[red]Invalid details command or error: {e}[/red]")
                continue_choice = Prompt.ask("Press Enter to go back to model list or type 'exit' to quit", default="back")
                if continue_choice.lower() == 'exit':
                    console.print("[yellow]Exiting...[/yellow]")
                    exit()
                continue
            else:
                try:
                    model_num = int(choice)
                    selected_model = sorted_models[model_num - 1]
                    console.print(f"[green]Selected model: {selected_model.id}[/green]")
                    return selected_model.id
                except:
                    console.print("[red]Invalid model number.[/red]")
        
    except Exception as e:
        console.print(f"[red]Error fetching models: {e}[/red]")
        return []

def list_all_models(client, console):
    try:
        with console.status("[bold cyan]Fetching all available models..."):
            models = client.models.list()
        console.print("\n[bold cyan]All Available Models:[/bold cyan]")
        models_table = Table(show_header=True, header_style="bold magenta")
        models_table.add_column("Index", style="dim", width=6)
        models_table.add_column("Model ID", style="green")
        models_table.add_column("Created", style="dim")
        
        all_models = [model for model in models.data if not (
            model.id.startswith('gpt-3.5') or 
            model.id.startswith('gpt-4') or 
            model.id.startswith('dall') or
            model.id.startswith('tts') or
            model.id.startswith('omni') or
            model.id.startswith('babbage') or
            model.id.startswith('chatgpt') or
            model.id.startswith('codex') or
            model.id.startswith('davinci') or
            model.id.startswith('gpt-image') or
            model.id.startswith('o1') or
            model.id.startswith('text-embedding') or
            re.match(r'^o\d', model.id) or
            model.id.startswith('whisper')
        )]
        
        unique_models = {}
        for model in all_models:
            base_without_date = re.sub(r'-\d{4}-\d{2}-\d{2}$', '', model.id)
            base_key = base_without_date
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', model.id)
            model_date = date_match.group(1) if date_match else "0000-00-00"
            if base_key not in unique_models or model_date > (re.search(r'(\d{4}-\d{2}-\d{2})', unique_models[base_key].id) or ["0000-00-00"])[0]:
                unique_models[base_key] = model
        
        sorted_models = sorted(unique_models.values(), key=lambda x: x.id)
        for i, model in enumerate(sorted_models, 1):
            created_date = datetime.fromtimestamp(model.created).strftime('%Y-%m-%d')
            models_table.add_row(str(i), model.id, created_date)
        
        console.print(models_table)
        console.print(f"\n[cyan]Total models available: {len(sorted_models)}[/cyan]")
        console.print("\n[bold yellow]Model Selection:[/bold yellow]")
        console.print("Enter model number to [green]use for research[/green], 'details <number>' for info, 'back' for main menu, or 'exit' to quit")
        
        while True:
            choice = Prompt.ask("Your choice", default="back")
            if choice == "exit":
                console.print("[yellow]Exiting...[/yellow]")
                exit()
            elif choice == "back":
                return [model.id for model in sorted_models]
            elif choice.startswith("details"):
                try:
                    model_num = int(choice.split()[1])
                    selected_model = sorted_models[model_num - 1]
                    console.print(f"\n[cyan]Fetching detailed information for {selected_model.id}...[/cyan]")
                    detailed_model = client.models.retrieve(selected_model.id)
                    console.print(f"\n[bold green]Detailed information for {selected_model.id}:[/bold green]")
                    detail_table = Table(show_header=True, header_style="bold magenta")
                    detail_table.add_column("Property", style="cyan")
                    detail_table.add_column("Value", style="green")
                    detail_table.add_row("ID", detailed_model.id)
                    detail_table.add_row("Object", getattr(detailed_model, 'object', 'unknown'))
                    detail_table.add_row("Created", datetime.fromtimestamp(detailed_model.created).strftime('%Y-%m-%d %H:%M:%S'))
                    console.print(detail_table)
                except Exception as e:
                    console.print(f"[red]Invalid details command or error: {e}[/red]")
                continue_choice = Prompt.ask("Press Enter to go back to model list or type 'exit' to quit", default="back")
                if continue_choice.lower() == 'exit':
                    console.print("[yellow]Exiting...[/yellow]")
                    exit()
                continue
            else:
                try:
                    model_num = int(choice)
                    selected_model = sorted_models[model_num - 1]
                    console.print(f"[green]Selected model: {selected_model.id}[/green]")
                    return selected_model.id
                except:
                    console.print("[red]Invalid model number.[/red]")
        
    except Exception as e:
        console.print(f"[red]Error fetching models: {e}[/red]")
        return []

def select_processing_option(console):
    console.print("\n[bold cyan]Research Model Options:[/bold cyan]")
    
    options_table = Table(show_header=True, header_style="bold magenta")
    options_table.add_column("Option", style="dim", width=8)
    options_table.add_column("Description", style="green")
    options_table.add_row("1", "Research with o3-2025-04-16 - Transcribe + analyze with o3-2025-04-16")
    options_table.add_row("2", "Exit", "Exit the application")
    
    console.print(options_table)
    
    choice = Prompt.ask("\nSelect option", choices=["1", "2"], default="1")
    if choice == "2":
        console.print("[yellow]Exiting...[/yellow]")
        exit()
    return int(choice)

def research_transcript(transcript_text, model, console, client):
    try:
        with console.status(f"[bold yellow]Analyzing transcript with {model}..."):
            messages = [
                {"role": "system", "content": "You are a helpful research assistant. Analyze the provided transcript and provide key insights, main points, action items, and a summary."},
                {"role": "user", "content": f"Please analyze this transcript and provide:\n1. Key insights\n2. Main points\n3. Action items (if any)\n4. Summary\n\nTranscript:\n{transcript_text}"}
            ]
            
            # Use different parameters for o3 models vs other models
            if model.startswith('o3'):
                response = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    max_completion_tokens=MAX_RESEARCH_TOKENS
                )
            else:
                response = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    max_tokens=MAX_RESEARCH_TOKENS,
                    temperature=RESEARCH_TEMPERATURE
                )
            return response.choices[0].message.content
    except Exception as e:
        console.print(f"[red]Error: Model {model} is not available or failed: {e}. Please check model access or API configuration.[/red]")
        return None

def format_research_paper(research_text, transcript_text, model_used, console, client):
    try:
        with console.status(f"[bold blue]Formatting research into paper structure with {PAPER_FORMATTING_MODEL}..."):
            messages = [
                {"role": "system", "content": "You are an expert academic writer. Format the provided research analysis into a proper research paper structure with clear sections, professional language, and academic formatting."},
                {"role": "user", "content": f"""Please format the following research analysis into a comprehensive research paper format with these sections:

1. EXECUTIVE SUMMARY
2. METHODOLOGY
3. KEY FINDINGS
4. DETAILED ANALYSIS
5. RECOMMENDATIONS
6. CONCLUSION

Research Analysis (conducted with {model_used}):
{research_text}

Original Transcript Context:
{transcript_text[:500]}...

Make it professional, well-structured, and suitable for business or academic presentation."""}            ]
            
            # Use different parameters for o3 models vs other models  
            if PAPER_FORMATTING_MODEL.startswith('o3'):
                response = client.chat.completions.create(
                    model=PAPER_FORMATTING_MODEL,
                    messages=messages,
                    max_completion_tokens=MAX_PAPER_TOKENS
                )
            else:
                response = client.chat.completions.create(
                    model=PAPER_FORMATTING_MODEL,
                    messages=messages,
                    max_tokens=MAX_PAPER_TOKENS,
                    temperature=PAPER_TEMPERATURE
                )
            return response.choices[0].message.content
    except Exception as e:
        console.print(f"[red]Error during paper formatting: {e}[/red]")
        return None

def save_research_paper(paper_text, original_filename, output_dir, model_used):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_name = os.path.splitext(original_filename)[0]
    
    # Save as text file
    txt_filename = f"{base_name}_{timestamp}_research_paper_{model_used.replace('-', '_')}.txt"
    txt_path = os.path.join(output_dir, txt_filename)
    with open(txt_path, "w", encoding="utf-8") as f:
        f.write(f"RESEARCH PAPER ANALYSIS\nSource Audio: {original_filename}\nResearch Model: {model_used}\nPaper Formatting: {PAPER_FORMATTING_MODEL}\nGenerated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n{'=' * 60}\n\n{paper_text}")
    
    # Save as Word document if available
    docx_path = None
    if DOCX_AVAILABLE:
        try:
            docx_filename = f"{base_name}_{timestamp}_research_paper_{model_used.replace('-', '_')}.docx"
            docx_path = os.path.join(output_dir, docx_filename)
            
            doc = Document()
            
            # Add title
            title = doc.add_heading('RESEARCH PAPER ANALYSIS', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add metadata
            doc.add_paragraph(f"Source Audio: {original_filename}")
            doc.add_paragraph(f"Research Model: {model_used}")
            doc.add_paragraph(f"Paper Formatting: {PAPER_FORMATTING_MODEL}")
            doc.add_paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            doc.add_paragraph()
            
            # Parse and format the paper content
            lines = paper_text.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    doc.add_paragraph()
                elif line.isupper() and len(line) < 50:  # Section headers
                    doc.add_heading(line, level=1)
                elif line.startswith(('1.', '2.', '3.', '4.', '5.', '6.')):  # Numbered sections
                    doc.add_heading(line, level=1)
                elif line.startswith('-') or line.startswith('•'):  # Bullet points
                    p = doc.add_paragraph(line[1:].strip())
                    p.style = 'List Bullet'
                else:  # Regular paragraph
                    doc.add_paragraph(line)
            
            doc.save(docx_path)
        except Exception as e:
            print(f"Warning: Could not create Word document: {e}")
            docx_path = None
    
    return txt_path, docx_path

def convert_to_supported_format(audio_file):
    supported_formats = ['.mp3', '.wav', '.m4a', '.mp4', '.mpeg', '.mpga', '.oga', '.ogg', '.webm', '.flac']
    
    if any(audio_file.lower().endswith(fmt) for fmt in supported_formats):
        return audio_file
    
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: ffmpeg is not installed or not found in PATH.")
        print("Please install ffmpeg:")
        print("  Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg")
        print("  Windows: Download from https://ffmpeg.org/download.html")
        print("  macOS: brew install ffmpeg")
        return None
    
    try:
        temp_mp3 = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
        temp_mp3.close()
        
        subprocess.run([
            'ffmpeg', '-i', audio_file, '-acodec', 'mp3', '-y', temp_mp3.name
        ], check=True, capture_output=True)
        
        return temp_mp3.name
    except subprocess.CalledProcessError as e:
        print(f"Error converting file: {e}")
        return None

def main():
    console = Console()
    console.print(Panel.fit("[bold cyan]Audio Transcription Tool[/bold cyan]\nSelect an audio file to transcribe using OpenAI Whisper", style="cyan"))
    api_key = get_or_prompt_api_key()
    client = OpenAI(api_key=api_key)
    selected_file = select_audio_file(console)
    if not selected_file:
        return
    console.print(f"\n[green]Processing file:[/green] {selected_file}")
    converted_file = convert_to_supported_format(selected_file)
    if not converted_file:
        console.print("[red]Failed to convert audio file to supported format.[/red]")
        return
    output_dir = create_output_folder()    # Check for cached transcript first
    cached_transcript, cached_output_path = get_cached_transcript(selected_file, console)
    if cached_transcript:
        console.print(f"[green]Using cached transcript:[/green] {cached_output_path}")
        console.print(Panel(cached_transcript, title="[bold cyan]Cached Transcript[/bold cyan]", expand=False))
        
        # Automatically use cached transcript for research analysis with o3-2025-04-16
        console.print("[green]Automatically using cached transcript for research analysis with o3-2025-04-16...[/green]")
        transcript_text = cached_transcript
        selected_model = "o3-2025-04-16"
        
        # Continue with research analysis
        console.print(f"\n[bold yellow]Starting research analysis with {selected_model}...[/bold yellow]")
        start_time = time.time()
        research_text = research_transcript(transcript_text, selected_model, console, client)
        end_time = time.time()
        analysis_duration = end_time - start_time
        
        if research_text:
            console.print(f"\n[bold blue]Formatting research paper with {PAPER_FORMATTING_MODEL}...[/bold blue]")
            paper_start_time = time.time()
            formatted_paper = format_research_paper(research_text, transcript_text, selected_model, console, client)
            paper_end_time = time.time()
            paper_duration = paper_end_time - paper_start_time
            if formatted_paper:
                paper_path, docx_path = save_research_paper(formatted_paper, selected_file, output_dir, selected_model)
                console.print(f"\n[bold green]✓ Research paper completed![/bold green]")
                console.print(f"[blue]Paper formatting took: {paper_duration:.2f} seconds[/blue]")
                console.print(f"[green]Total analysis time: {analysis_duration:.2f} seconds[/green]")
                console.print(f"[green]Text file saved to:[/green] {paper_path}")
                if docx_path:
                    console.print(f"[green]Word document saved to:[/green] {docx_path}")
                else:
                    console.print("[yellow]Note: Install python-docx for Word document output[/yellow]")
                console.print(Panel(formatted_paper, title=f"[bold green]Research Paper ({selected_model} + {PAPER_FORMATTING_MODEL}) - Total: {analysis_duration + paper_duration:.2f}s[/bold green]", expand=False))

    # Directly use o3-2025-04-16 for new transcriptions
    selected_model = "o3-2025-04-16"
    console.print(f"[green]Using {selected_model} for research analysis[/green]")
        
    temp_file = None
    try:
        if converted_file != selected_file:
            temp_file = converted_file
        
        # Add transcription timing and progress
        console.print(f"\n[bold green]Starting transcription with Whisper...[/bold green]")
        transcription_start_time = time.time()
        
        with console.status("[bold green]Transcribing audio..."):
            with open(converted_file, "rb") as audio_file:
                transcript = client.audio.transcriptions.create(model="whisper-1", file=audio_file)
        
        transcription_end_time = time.time()
        transcription_duration = transcription_end_time - transcription_start_time
        
        transcript_text = transcript.text
        output_path = save_transcript(transcript_text, selected_file, output_dir)
        cache_transcript(selected_file, transcript_text, output_path, selected_file)
        
        console.print(f"\n[bold green]✓ Transcription completed in {transcription_duration:.2f} seconds![/bold green]")
        console.print(f"[green]Saved to:[/green] {output_path}")
        console.print(Panel(transcript_text, title="[bold cyan]Transcript[/bold cyan]", expand=False))
        console.print(f"\n[bold yellow]Starting research analysis with {selected_model}...[/bold yellow]")
        start_time = time.time()
        research_text = research_transcript(transcript_text, selected_model, console, client)
        end_time = time.time()
        analysis_duration = end_time - start_time
        
        if research_text:
            console.print(f"\n[bold blue]Formatting research paper with {PAPER_FORMATTING_MODEL}...[/bold blue]")
            paper_start_time = time.time()
            formatted_paper = format_research_paper(research_text, transcript_text, selected_model, console, client)
            paper_end_time = time.time()
            paper_duration = paper_end_time - paper_start_time
            if formatted_paper:
                paper_path = save_research_paper(formatted_paper, selected_file, output_dir, selected_model)
                console.print(f"\n[bold green]✓ Research paper completed![/bold green]")
                console.print(f"[blue]Paper formatting took: {paper_duration:.2f} seconds[/blue]")
                console.print(f"[green]Total analysis time: {analysis_duration:.2f} seconds[/green]")
                console.print(f"[green]Paper saved to:[/green] {paper_path}")
                console.print(Panel(formatted_paper, title=f"[bold green]Research Paper ({selected_model} + {PAPER_FORMATTING_MODEL}) - Total: {analysis_duration + paper_duration:.2f}s[/bold green]", expand=False))
    except Exception as e:
        console.print(f"[red]Error during transcription: {e}[/red]")
    finally:
        if temp_file and os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    main()