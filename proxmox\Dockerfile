FROM node:lts-slim
ENV DEBIAN_FRONTEND=noninteractive \
    PIP_NO_CACHE_DIR=1 \
    PYTHONDONTWRITEBYTECODE=1

# Base deps
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3 python3-pip curl gnupg ca-certificates unzip jq && \
    rm -rf /var/lib/apt/lists/*

# Terraform (APT, fallback to direct download if APT fails)
RUN set -eux; \
  if curl -fsSL https://apt.releases.hashicorp.com/gpg | \
       gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg; then \
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(. /etc/os-release && echo $VERSION_CODENAME) main" \
      > /etc/apt/sources.list.d/hashicorp.list && \
    apt-get update && apt-get install -y --no-install-recommends terraform && \
    rm -rf /var/lib/apt/lists/*; \
  else \
    echo "APT repo unreachable, falling back to direct download of latest Terraform"; \
    TF_VERSION="$(curl -fsSL https://checkpoint-api.hashicorp.com/v1/check/terraform | jq -r .current_version)"; \
    curl -fsSLo /tmp/terraform.zip "https://releases.hashicorp.com/terraform/${TF_VERSION}/terraform_${TF_VERSION}_linux_amd64.zip"; \
    unzip -d /usr/local/bin /tmp/terraform.zip && rm -f /tmp/terraform.zip; \
    terraform -version; \
  fi

# CDKTF CLI
RUN npm install -g --omit=dev cdktf-cli@latest && npm cache clean --force

# Install Python packages globally (avoid pip upgrade to prevent corruption)
RUN python3 -m pip install --break-system-packages \
      constructs \
      cdktf \
      jsii \
      cdktf-cdktf-provider-kubernetes \
      cdktf-cdktf-provider-helm

# Non-root user + workspace
RUN useradd -m app && mkdir -p /workspace && chown -R app:app /workspace
USER app
WORKDIR /workspace
