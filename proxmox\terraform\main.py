#!/usr/bin/env python3
from constructs import Construct
from cdktf import (
    App, TerraformStack, TerraformVariable, TerraformOutput, Token
)
from cdktf_cdktf_provider_kubernetes.provider import KubernetesProvider
from cdktf_cdktf_provider_kubernetes.namespace import Namespace, NamespaceMetadata
from ctypes import c_void_p  # no-op import guard for some linters (optional)
from cdktf_cdktf_provider_helm.provider import <PERSON><PERSON><PERSON><PERSON><PERSON>, HelmProviderKubernetes
from cdktf_cdktf_provider_helm.release import Release, ReleaseSet


class HeadlampStack(TerraformStack):
    def __init__(self, scope: Construct, ns: str):
        super().__init__(scope, ns)

        # ---------------- Variables (match your HCL) ----------------
        cluster_name = TerraformVariable(self, "cluster_name", default="dev")
        domain       = TerraformVariable(self, "domain",       default="home.lan")
        lb_ip        = TerraformVariable(self, "lb_ip",        default="*************")
        lb_pool      = TerraformVariable(self, "lb_pool",      default="*************-*************")
        nfs_base     = TerraformVariable(self, "nfs_base",     default="/srv/eks")
        client_cidr  = TerraformVariable(self, "client_cidr",  default="***********/24")
        host_ip      = TerraformVariable(self, "host_ip",      default="*************")

        kubeconfig_override = TerraformVariable(
            self,
            "kubeconfig_override",
            type="string",
            default="",
            description="Optional explicit path to kubeconfig. If empty, uses ./<cluster_name>/<cluster_name>-eks-a-cluster.kubeconfig",
        )

        # ---------------- Locals / expressions ----------------
        # Fallback logic implemented as a raw Terraform expression so it works at apply time:
        # pathexpand(trimspace(var.kubeconfig_override) != "" ? trimspace(var.kubeconfig_override)
        #                                                     : "${path.module}/${var.cluster_name}/${var.cluster_name}-eks-a-cluster.kubeconfig")
        kubeconfig_path = '${pathexpand(trimspace(var.kubeconfig_override) != "" ? trimspace(var.kubeconfig_override) : "${path.module}/${var.cluster_name}/${var.cluster_name}-eks-a-cluster.kubeconfig")}'

        headlamp_namespace = "headlamp"
        headlamp_release   = "headlamp"
        headlamp_host      = f"headlamp.{Token.as_string(cluster_name.string_value)}.{Token.as_string(domain.string_value)}"

        # ---------------- Providers ----------------
        KubernetesProvider(self, "k8s", config_path=kubeconfig_path)
        HelmProvider(self, "helm", kubernetes=HelmProviderKubernetes(config_path=kubeconfig_path))

        # ---------------- Namespace ----------------
        ns_res = Namespace(self, "headlamp_ns", metadata=NamespaceMetadata(name=headlamp_namespace))

        # ---------------- Headlamp Helm release ----------------
        Release(
            self,
            "headlamp",
            name=headlamp_release,
            namespace=headlamp_namespace,
            repository="https://kubernetes-sigs.github.io/headlamp/",
            chart="headlamp",
            set=[
                ReleaseSet(name="service.type", value="ClusterIP"),
                ReleaseSet(name="ingress.enabled", value="true"),
                ReleaseSet(name="ingress.ingressClassName", value="nginx"),
                ReleaseSet(name="ingress.hosts[0].host", value=headlamp_host),
                ReleaseSet(name="ingress.hosts[0].paths[0].path", value="/"),
                ReleaseSet(name="ingress.hosts[0].paths[0].pathType", value="Prefix"),
            ],
            depends_on=[ns_res],
        )

        # ---------------- Outputs ----------------
        TerraformOutput(self, "cluster_kubeconfig", value=kubeconfig_path)
        TerraformOutput(
            self,
            "ingress_hint",
            value=(
                f"If NodePort: http://<node-ip>:<http-nodeport>. "
                f"If LoadBalancer: map {headlamp_host} -> {Token.as_string(lb_ip.string_value)}"
            ),
        )
        TerraformOutput(
            self,
            "runbook_notes",
            value=(
                f"Cluster      : {Token.as_string(cluster_name.string_value)}\n"
                f"Domain       : {Token.as_string(domain.string_value)}\n"
                f"MetalLB Pool : {Token.as_string(lb_pool.string_value)}\n"
                f"Ingress IP   : {Token.as_string(lb_ip.string_value)}\n"
                f"NFS Base     : {Token.as_string(nfs_base.string_value)}\n"
                f"NFS Clients  : {Token.as_string(client_cidr.string_value)}\n"
                f"Host (NFS)   : {Token.as_string(host_ip.string_value)}\n"
            ),
        )


app = App()
HeadlampStack(app, "headlamp-stack")
app.synth()
