import asyncio
import websockets
import json
import keyring
import getpass
import ssl

# === CONFIG ===
XO_HOST = "*************"
XO_WS_URL = f"wss://{XO_HOST}/api/"
XO_USER = "<EMAIL>"
SR_UUID = "f3f8ce67-26e2-b3f3-0963-cf3440a0bc2d"
ISO_URL = "https://releases.ubuntu.com/25.04/ubuntu-25.04-live-server-amd64.iso"

# === AUTH ===
SERVICE_NAME = "xo-api"
stored_pw = keyring.get_password(SERVICE_NAME, XO_USER)
if not stored_pw:
    pw = getpass.getpass("Enter XO password: ")
    keyring.set_password(SERVICE_NAME, XO_USER, pw)
else:
    pw = stored_pw

# === MAIN LOGIC ===
async def run():
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    try:
        print(f"[🌐] Connecting to {XO_WS_URL}...")
        async with websockets.connect(XO_WS_URL, ssl=ssl_context) as ws:
            print("[✅] WebSocket connection established")
            
            # 1. Authenticate
            auth_msg = {
                "id": 1,
                "method": "session.signIn",
                "params": {
                    "email": XO_USER,
                    "password": pw
                }
            }
            print(f"[📤] Sending auth request...")
            await ws.send(json.dumps(auth_msg))
            
            print("[📡] Waiting for auth response...")
            response = await ws.recv()
            print(f"[✅] Auth Response: {response}")
            
            # Parse response to check for errors
            auth_result = json.loads(response)
            if "error" in auth_result:
                print(f"[❌] Authentication failed: {auth_result['error']}")
                return
            
            print("[🔑] Authentication successful!")

            # 2. Call disk.import
            import_msg = {
                "id": 2,
                "method": "disk.import",
                "params": {
                    "url": ISO_URL,
                    "sr": SR_UUID
                }
            }
            print(f"[📤] Sending import request...")
            await ws.send(json.dumps(import_msg))
            
            print("[📡] Waiting for import response...")
            import_response = await ws.recv()
            print(f"[✅] Import Response: {import_response}")
            
    except Exception as e:
        print(f"[❌] Error: {e}")
        print(f"[ℹ️] Make sure XenOrchestra is running at {XO_HOST}")

asyncio.run(run())
